/**
 * AutoX.js 简单演示程序
 * 这个脚本演示了AutoX.js的基本功能
 */

// 检查无障碍服务权限
auto.waitFor();

// 显示控制台
console.show();
console.log("AutoX.js Demo 开始运行...");

// 主菜单函数
function showMainMenu() {
    let options = [
        "1. 基础UI操作演示",
        "2. 文本识别演示", 
        "3. 图像识别演示",
        "4. 手势操作演示",
        "5. 应用管理演示",
        "6. 退出程序"
    ];
    
    let choice = dialogs.select("请选择要演示的功能", options);
    return choice;
}

// 1. 基础UI操作演示
function basicUIDemo() {
    console.log("=== 基础UI操作演示 ===");
    
    // 显示Toast消息
    toast("这是一个Toast消息");
    sleep(2000);
    
    // 显示对话框
    dialogs.alert("提示", "这是一个警告对话框");
    
    // 输入对话框
    let userInput = dialogs.rawInput("请输入一些文字", "默认文本");
    if (userInput) {
        toast("您输入的是: " + userInput);
    }
    
    console.log("基础UI操作演示完成");
}

// 2. 文本识别演示
function textRecognitionDemo() {
    console.log("=== 文本识别演示 ===");
    
    // 查找包含特定文本的控件
    let targetText = "设置";
    let widget = text(targetText).findOne(3000);
    
    if (widget) {
        console.log("找到包含'" + targetText + "'的控件");
        console.log("控件位置: " + widget.bounds());
        toast("找到目标文本控件");
    } else {
        console.log("未找到包含'" + targetText + "'的控件");
        toast("未找到目标文本");
    }
    
    // 获取屏幕上所有文本
    let allTexts = textMatches(/.+/).find();
    console.log("屏幕上共找到 " + allTexts.length + " 个文本控件");
    
    // 显示前5个文本内容
    for (let i = 0; i < Math.min(5, allTexts.length); i++) {
        console.log("文本 " + (i+1) + ": " + allTexts[i].text());
    }
}

// 3. 图像识别演示
function imageRecognitionDemo() {
    console.log("=== 图像识别演示 ===");
    
    // 请求截图权限
    if (!requestScreenCapture()) {
        toast("请授予截图权限");
        return;
    }
    
    // 截取屏幕
    let img = captureScreen();
    if (img) {
        console.log("截图成功，图片尺寸: " + img.getWidth() + "x" + img.getHeight());
        toast("截图成功");
        
        // 保存截图到文件
        let fileName = "/sdcard/autojs_demo_screenshot.png";
        images.save(img, fileName);
        console.log("截图已保存到: " + fileName);
        
        // 回收图片资源
        img.recycle();
    } else {
        console.log("截图失败");
        toast("截图失败");
    }
}

// 4. 手势操作演示
function gestureDemo() {
    console.log("=== 手势操作演示 ===");
    
    let choice = dialogs.select("选择手势操作", [
        "点击屏幕中心",
        "滑动操作",
        "长按操作",
        "返回"
    ]);
    
    switch(choice) {
        case 0:
            // 点击屏幕中心
            let centerX = device.width / 2;
            let centerY = device.height / 2;
            console.log("点击屏幕中心: (" + centerX + ", " + centerY + ")");
            click(centerX, centerY);
            toast("已点击屏幕中心");
            break;
            
        case 1:
            // 滑动操作
            console.log("执行向上滑动");
            swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
            toast("已执行向上滑动");
            break;
            
        case 2:
            // 长按操作
            console.log("长按屏幕中心");
            longClick(device.width / 2, device.height / 2);
            toast("已长按屏幕中心");
            break;
            
        case 3:
            return;
    }
}

// 5. 应用管理演示
function appManagementDemo() {
    console.log("=== 应用管理演示 ===");
    
    // 获取当前运行的应用
    let currentApp = currentPackage();
    console.log("当前应用包名: " + currentApp);
    
    // 获取已安装的应用列表（显示前10个）
    let installedApps = getInstalledApps();
    console.log("已安装应用数量: " + installedApps.length);
    
    console.log("前10个已安装应用:");
    for (let i = 0; i < Math.min(10, installedApps.length); i++) {
        let app = installedApps[i];
        console.log((i+1) + ". " + app.appName + " (" + app.packageName + ")");
    }
    
    // 演示打开设置应用
    let openSettings = dialogs.confirm("演示", "是否打开系统设置应用？");
    if (openSettings) {
        app.startActivity({
            action: "android.settings.SETTINGS"
        });
        toast("已打开设置应用");
        sleep(3000);
        back(); // 返回
    }
}

// 主程序循环
function main() {
    console.log("AutoX.js Demo 启动");
    toast("欢迎使用 AutoX.js Demo");
    
    while (true) {
        let choice = showMainMenu();
        
        switch(choice) {
            case 0:
                basicUIDemo();
                break;
            case 1:
                textRecognitionDemo();
                break;
            case 2:
                imageRecognitionDemo();
                break;
            case 3:
                gestureDemo();
                break;
            case 4:
                appManagementDemo();
                break;
            case 5:
                console.log("程序退出");
                toast("再见！");
                exit();
                break;
            default:
                console.log("程序退出");
                exit();
                break;
        }
        
        // 每次演示后暂停
        sleep(1000);
    }
}

// 启动主程序
main();
