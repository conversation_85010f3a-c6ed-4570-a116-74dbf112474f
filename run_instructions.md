# AutoX.js 独立运行指南

## 🖥️ 电脑上运行（推荐）

### 方式1: Android Studio模拟器
1. 下载安装 Android Studio
2. 创建Android虚拟设备(AVD)
3. 启动模拟器
4. 在模拟器中安装AutoX.js
5. 运行脚本

### 方式2: 夜神/雷电模拟器
1. 下载夜神模拟器或雷电模拟器
2. 安装并启动模拟器
3. 在模拟器中安装AutoX.js APK
4. 导入脚本文件运行

### 方式3: WSA (Windows 11)
1. 启用Windows Subsystem for Android
2. 安装AutoX.js
3. 运行脚本

## 🌐 在线运行

### 方式4: 在线JavaScript环境
可以将部分逻辑改写为标准JavaScript在浏览器中运行

### 方式5: Node.js环境
将AutoX.js语法转换为Node.js兼容代码

## 📱 无需手机的测试版本

我可以为你创建一个模拟版本，在浏览器中演示功能
