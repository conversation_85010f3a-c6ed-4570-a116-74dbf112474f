# 游戏辅助工具部署指南

## 🎮 独立运行的游戏辅助系统

这是一个完整的游戏辅助解决方案，可以独立运行并自动化各种手机游戏操作。

## 📁 项目结构

```
autoJs/
├── game_assistant.js          # 基础游戏辅助工具
├── advanced_game_bot.js       # 高级AI游戏机器人
├── main.js                    # 基础演示程序
├── deployment_guide.md        # 部署指南
├── project.json              # 项目配置
└── examples/                 # 示例脚本
    ├── simple_automation.js
    └── image_processing.js
```

## 🚀 独立运行方式

### 方式1: 打包为APK (推荐)

1. **使用AutoX.js打包功能**
   ```
   - 在AutoX.js中打开项目
   - 选择"打包应用"
   - 配置应用信息
   - 生成独立APK文件
   ```

2. **安装到目标设备**
   ```
   - 将APK传输到目标设备
   - 安装APK
   - 授予必要权限
   - 直接运行，无需AutoX.js环境
   ```

### 方式2: 脚本独立运行

1. **配置自启动**
   ```javascript
   // 在脚本开头添加
   auto.waitFor();
   
   // 设置开机自启动
   auto.setMode("fast");
   
   // 隐藏控制台（可选）
   console.hide();
   ```

2. **后台运行**
   ```javascript
   // 设置为服务模式
   engines.execScriptFile("game_assistant.js", {
       delay: 0,
       interval: 0,
       loopTimes: 1
   });
   ```

## 🎯 游戏辅助功能

### 基础版本 (game_assistant.js)

**支持的游戏类型:**
- 消除类游戏 (三消、宝石等)
- 放置类游戏 (挂机、点击等)
- 卡牌类游戏 (回合制、策略等)
- 通用游戏辅助

**核心功能:**
- 颜色识别点击
- 自动收集奖励
- 循环操作执行
- 运行统计分析

### 高级版本 (advanced_game_bot.js)

**AI智能功能:**
- 游戏状态分析
- 智能决策引擎
- 模式学习能力
- 人性化操作

**高级特性:**
- 图像识别分析
- 策略自动优化
- 反检测机制
- 详细日志记录

## ⚙️ 配置说明

### 基础配置
```javascript
const CONFIG = {
    clickInterval: 1000,    // 点击间隔(ms)
    checkInterval: 500,     // 检查间隔(ms)
    colorThreshold: 20,     // 颜色识别阈值
    screenshotPath: "/sdcard/game_assistant/"
};
```

### 高级配置
```javascript
const ADVANCED_CONFIG = {
    learningMode: true,         // 启用学习模式
    decisionThreshold: 0.8,     // 决策置信度阈值
    humanLikeDelay: true,       // 人性化延迟
    antiDetection: true,        // 反检测功能
    randomDelay: [500, 2000]    // 随机延迟范围
};
```

## 🎮 适配新游戏

### 1. 添加游戏配置
```javascript
const GAMES = {
    "你的游戏名": {
        packageName: "com.example.yourgame",
        features: ["功能1", "功能2"],
        colors: {
            button_color: "#FF0000",
            reward_color: "#FFD700"
        }
    }
};
```

### 2. 实现游戏逻辑
```javascript
function yourGameAssist() {
    console.log("=== 你的游戏辅助 ===");
    
    while (this.isRunning) {
        // 实现具体的游戏操作逻辑
        if (this.clickColor("#FF0000")) {
            console.log("执行了游戏操作");
        }
        sleep(CONFIG.checkInterval);
    }
}
```

## 🛡️ 安全与合规

### 反检测机制
- 随机化操作时间
- 模拟人类操作模式
- 避免过于规律的行为
- 适当的休息间隔

### 使用建议
- 仅用于单机游戏或允许辅助的游戏
- 避免在竞技游戏中使用
- 遵守游戏服务条款
- 注意账号安全

## 📊 性能优化

### 内存管理
```javascript
// 及时释放图片资源
let img = captureScreen();
// ... 使用图片
img.recycle(); // 重要：释放内存
```

### 性能调优
```javascript
// 降低截图频率
sleep(CONFIG.checkInterval);

// 减少颜色搜索区域
let region = [x, y, width, height];
images.findColor(img, color, {region: region});
```

## 🔧 故障排除

### 常见问题

1. **权限问题**
   - 确保开启无障碍服务
   - 授予截图权限
   - 允许悬浮窗显示

2. **识别失败**
   - 调整颜色阈值
   - 检查屏幕分辨率
   - 验证颜色值准确性

3. **性能问题**
   - 增加操作间隔
   - 减少截图频率
   - 优化搜索区域

### 调试技巧
```javascript
// 开启详细日志
console.show();
console.log("调试信息");

// 保存调试截图
let img = captureScreen();
images.save(img, "/sdcard/debug.png");
img.recycle();

// 显示点击位置
function debugClick(x, y) {
    console.log(`点击位置: (${x}, ${y})`);
    click(x, y);
}
```

## 📈 扩展开发

### 添加新功能
1. 图像模板匹配
2. OCR文字识别
3. 网络数据交互
4. 云端配置同步
5. 多设备协同

### 集成第三方服务
- 云端AI识别
- 数据统计分析
- 远程控制接口
- 自动更新机制

## 📞 技术支持

如需技术支持或功能定制，可以：
1. 查看AutoX.js官方文档
2. 参考项目示例代码
3. 加入相关技术社区
4. 进行二次开发定制

---

**免责声明**: 本工具仅供学习和研究使用，使用者需自行承担使用风险，遵守相关法律法规和游戏服务条款。
