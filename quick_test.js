/**
 * 快速测试脚本
 * 用于验证AutoX.js环境是否正常
 */

// 检查无障碍服务
if (!auto.service) {
    toast("请先开启无障碍服务");
    auto.waitFor();
}

// 显示控制台
console.show();
console.log("AutoX.js 环境测试开始...");

// 测试1: 基础功能
console.log("✓ 控制台显示正常");
toast("✓ Toast消息正常");

// 测试2: 设备信息
console.log("设备信息:");
console.log("- 屏幕尺寸: " + device.width + "x" + device.height);
console.log("- Android版本: " + device.release);
console.log("- 设备型号: " + device.model);

// 测试3: 对话框
dialogs.alert("测试", "如果看到这个对话框，说明AutoX.js运行正常！");

console.log("✓ 环境测试完成，AutoX.js运行正常！");
toast("测试完成！");
