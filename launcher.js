/**
 * 游戏辅助启动器
 * 统一入口，选择不同的辅助模式
 */

auto.waitFor();
console.show();

// 启动器配置
const LAUNCHER_CONFIG = {
    appName: "游戏辅助工具集",
    version: "2.0.0",
    author: "AutoX.js Demo"
};

// 显示启动画面
function showSplashScreen() {
    console.clear();
    console.log("=".repeat(40));
    console.log(`    ${LAUNCHER_CONFIG.appName}`);
    console.log(`    版本: ${LAUNCHER_CONFIG.version}`);
    console.log(`    作者: ${LAUNCHER_CONFIG.author}`);
    console.log("=".repeat(40));
    console.log("");
    
    toast(`${LAUNCHER_CONFIG.appName} 启动中...`);
    sleep(2000);
}

// 检查运行环境
function checkEnvironment() {
    let issues = [];
    
    // 检查无障碍服务
    if (!auto.service) {
        issues.push("无障碍服务未开启");
    }
    
    // 检查截图权限
    if (!requestScreenCapture(false)) {
        issues.push("截图权限未授予");
    }
    
    // 检查存储权限
    try {
        files.ensureDir("/sdcard/game_assistant/");
    } catch (e) {
        issues.push("存储权限不足");
    }
    
    if (issues.length > 0) {
        let message = "发现以下问题:\n" + issues.join("\n") + "\n\n是否继续运行?";
        return dialogs.confirm("环境检查", message);
    }
    
    return true;
}

// 主菜单
function showMainMenu() {
    let options = [
        "🎮 基础游戏辅助",
        "🤖 智能AI机器人", 
        "📚 功能演示",
        "🔧 工具箱",
        "⚙️ 设置",
        "ℹ️ 关于",
        "❌ 退出"
    ];
    
    return dialogs.select(LAUNCHER_CONFIG.appName, options);
}

// 启动基础游戏辅助
function launchBasicAssistant() {
    try {
        console.log("启动基础游戏辅助...");
        engines.execScriptFile("game_assistant.js");
        toast("基础游戏辅助已启动");
    } catch (e) {
        dialogs.alert("错误", "无法启动基础游戏辅助: " + e.message);
    }
}

// 启动智能AI机器人
function launchAdvancedBot() {
    try {
        console.log("启动智能AI机器人...");
        engines.execScriptFile("advanced_game_bot.js");
        toast("智能AI机器人已启动");
    } catch (e) {
        dialogs.alert("错误", "无法启动智能AI机器人: " + e.message);
    }
}

// 启动功能演示
function launchDemo() {
    try {
        console.log("启动功能演示...");
        engines.execScriptFile("main.js");
        toast("功能演示已启动");
    } catch (e) {
        dialogs.alert("错误", "无法启动功能演示: " + e.message);
    }
}

// 工具箱菜单
function showToolbox() {
    let tools = [
        "📱 设备信息",
        "📸 截图测试",
        "🎨 颜色拾取器",
        "📊 性能监控",
        "🗂️ 文件管理",
        "🔙 返回主菜单"
    ];
    
    let choice = dialogs.select("工具箱", tools);
    
    switch (choice) {
        case 0:
            showDeviceInfo();
            break;
        case 1:
            testScreenshot();
            break;
        case 2:
            launchColorPicker();
            break;
        case 3:
            showPerformanceMonitor();
            break;
        case 4:
            openFileManager();
            break;
        case 5:
            return;
    }
}

// 显示设备信息
function showDeviceInfo() {
    let info = `设备型号: ${device.model}\n` +
              `Android版本: ${device.release}\n` +
              `屏幕尺寸: ${device.width}x${device.height}\n` +
              `屏幕密度: ${device.density}\n` +
              `SDK版本: ${device.sdkInt}`;
    
    dialogs.alert("设备信息", info);
}

// 测试截图功能
function testScreenshot() {
    if (!requestScreenCapture()) {
        dialogs.alert("错误", "无法获取截图权限");
        return;
    }
    
    toast("正在截图...");
    let img = captureScreen();
    
    if (img) {
        let fileName = `/sdcard/game_assistant/test_${Date.now()}.png`;
        images.save(img, fileName);
        img.recycle();
        
        dialogs.alert("截图成功", `截图已保存到: ${fileName}`);
    } else {
        dialogs.alert("错误", "截图失败");
    }
}

// 颜色拾取器
function launchColorPicker() {
    if (!requestScreenCapture()) {
        dialogs.alert("错误", "需要截图权限");
        return;
    }
    
    dialogs.alert("颜色拾取器", "点击确定后，触摸屏幕任意位置获取颜色值");
    
    // 简单的颜色拾取实现
    let img = captureScreen();
    if (img) {
        // 获取屏幕中心点颜色作为示例
        let centerX = img.getWidth() / 2;
        let centerY = img.getHeight() / 2;
        let color = images.pixel(img, centerX, centerY);
        
        let colorInfo = `屏幕中心点颜色:\n` +
                       `十六进制: ${colors.toString(color)}\n` +
                       `RGB: (${colors.red(color)}, ${colors.green(color)}, ${colors.blue(color)})`;
        
        dialogs.alert("颜色信息", colorInfo);
        img.recycle();
    }
}

// 性能监控
function showPerformanceMonitor() {
    let memInfo = `可用内存: ${device.getTotalMemory() / 1024 / 1024}MB\n` +
                 `电池电量: ${device.getBattery()}%\n` +
                 `CPU使用率: 监控中...`;
    
    dialogs.alert("性能监控", memInfo);
}

// 文件管理
function openFileManager() {
    let files_list = files.listDir("/sdcard/game_assistant/");
    if (files_list.length === 0) {
        dialogs.alert("文件管理", "目录为空");
        return;
    }
    
    let choice = dialogs.select("选择文件", files_list);
    if (choice >= 0) {
        let fileName = files_list[choice];
        dialogs.alert("文件信息", `文件名: ${fileName}\n路径: /sdcard/game_assistant/${fileName}`);
    }
}

// 设置菜单
function showSettings() {
    let settings = [
        "🔧 基础设置",
        "🤖 AI设置", 
        "🛡️ 安全设置",
        "📝 日志设置",
        "🔄 重置设置",
        "🔙 返回"
    ];
    
    let choice = dialogs.select("设置", settings);
    
    switch (choice) {
        case 0:
            dialogs.alert("基础设置", "点击间隔、检查频率等基础参数配置");
            break;
        case 1:
            dialogs.alert("AI设置", "学习模式、决策阈值等AI参数配置");
            break;
        case 2:
            dialogs.alert("安全设置", "反检测、人性化操作等安全功能配置");
            break;
        case 3:
            dialogs.alert("日志设置", "日志级别、保存路径等日志相关配置");
            break;
        case 4:
            if (dialogs.confirm("确认", "是否重置所有设置?")) {
                toast("设置已重置");
            }
            break;
        case 5:
            return;
    }
}

// 关于信息
function showAbout() {
    let aboutText = `${LAUNCHER_CONFIG.appName}\n\n` +
                   `版本: ${LAUNCHER_CONFIG.version}\n` +
                   `作者: ${LAUNCHER_CONFIG.author}\n\n` +
                   `这是一个基于AutoX.js开发的游戏辅助工具集，\n` +
                   `包含基础辅助和智能AI两种模式。\n\n` +
                   `功能特点:\n` +
                   `• 多种游戏类型支持\n` +
                   `• 智能决策引擎\n` +
                   `• 人性化操作模拟\n` +
                   `• 详细运行统计\n\n` +
                   `使用前请确保:\n` +
                   `• 已开启无障碍服务\n` +
                   `• 已授予必要权限\n` +
                   `• 遵守游戏服务条款`;
    
    dialogs.alert("关于", aboutText);
}

// 主程序
function main() {
    // 显示启动画面
    showSplashScreen();
    
    // 检查运行环境
    if (!checkEnvironment()) {
        console.log("用户取消运行");
        return;
    }
    
    console.log("环境检查通过，启动主程序");
    
    // 主循环
    while (true) {
        let choice = showMainMenu();
        
        switch (choice) {
            case 0:
                launchBasicAssistant();
                break;
            case 1:
                launchAdvancedBot();
                break;
            case 2:
                launchDemo();
                break;
            case 3:
                showToolbox();
                break;
            case 4:
                showSettings();
                break;
            case 5:
                showAbout();
                break;
            case 6:
                console.log("程序退出");
                toast("再见!");
                exit();
                break;
            default:
                exit();
                break;
        }
        
        sleep(500);
    }
}

// 启动程序
main();
