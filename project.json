{"name": "AutoX.js Demo", "version": "1.0.0", "description": "AutoX.js 基础功能演示程序", "main": "main.js", "author": "AutoX.js Demo", "packageName": "com.example.autoxjs.demo", "versionCode": 1, "build": {"build_id": "demo_v1.0.0"}, "scripts": {"main": "main.js"}, "permissions": ["android.permission.SYSTEM_ALERT_WINDOW", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"], "launchConfig": {"displaySplash": true, "hideLogs": false, "stableMode": false}}