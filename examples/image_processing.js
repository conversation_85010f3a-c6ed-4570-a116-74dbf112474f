/**
 * 图像处理示例
 * 演示AutoX.js的图像处理和识别功能
 */

// 确保无障碍服务已开启
auto.waitFor();
console.show();

// 请求截图权限
if (!requestScreenCapture()) {
    toast("请授予截图权限");
    exit();
}

// 示例1: 基础截图功能
function basicScreenshotDemo() {
    console.log("=== 基础截图演示 ===");
    
    let img = captureScreen();
    if (img) {
        console.log("截图成功");
        console.log("图片尺寸: " + img.getWidth() + "x" + img.getHeight());
        
        // 保存截图
        let timestamp = new Date().getTime();
        let fileName = "/sdcard/screenshot_" + timestamp + ".png";
        images.save(img, fileName);
        console.log("截图已保存: " + fileName);
        
        img.recycle();
        toast("截图保存成功");
    }
}

// 示例2: 区域截图
function regionScreenshotDemo() {
    console.log("=== 区域截图演示 ===");
    
    let img = captureScreen();
    if (img) {
        // 截取屏幕中央区域
        let centerX = img.getWidth() / 2;
        let centerY = img.getHeight() / 2;
        let size = 200;
        
        let regionImg = images.clip(img, centerX - size/2, centerY - size/2, size, size);
        
        if (regionImg) {
            let fileName = "/sdcard/region_screenshot.png";
            images.save(regionImg, fileName);
            console.log("区域截图已保存: " + fileName);
            regionImg.recycle();
        }
        
        img.recycle();
        toast("区域截图完成");
    }
}

// 示例3: 颜色识别
function colorDetectionDemo() {
    console.log("=== 颜色识别演示 ===");
    
    let img = captureScreen();
    if (img) {
        // 获取屏幕中心点的颜色
        let centerX = img.getWidth() / 2;
        let centerY = img.getHeight() / 2;
        let color = images.pixel(img, centerX, centerY);
        
        console.log("屏幕中心点颜色: " + colors.toString(color));
        console.log("RGB值: R=" + colors.red(color) + " G=" + colors.green(color) + " B=" + colors.blue(color));
        
        // 查找相似颜色的点
        let similarPoints = images.findColor(img, color, {
            threshold: 50,
            region: [centerX - 100, centerY - 100, 200, 200]
        });
        
        if (similarPoints) {
            console.log("找到相似颜色点: (" + similarPoints.x + ", " + similarPoints.y + ")");
        }
        
        img.recycle();
        toast("颜色识别完成");
    }
}

// 示例4: 图像比较
function imageComparisonDemo() {
    console.log("=== 图像比较演示 ===");
    
    // 截取两张图片进行比较
    toast("即将截取第一张图片");
    sleep(2000);
    let img1 = captureScreen();
    
    toast("请稍微移动屏幕内容，3秒后截取第二张图片");
    sleep(3000);
    let img2 = captureScreen();
    
    if (img1 && img2) {
        // 比较两张图片的相似度
        try {
            let similarity = images.matchTemplate(img1, img2, {
                threshold: 0.8,
                max_results: 1
            });
            
            if (similarity.matches && similarity.matches.length > 0) {
                console.log("图片相似度较高");
            } else {
                console.log("图片差异较大");
            }
        } catch (e) {
            console.log("图片比较出错: " + e.message);
        }
        
        img1.recycle();
        img2.recycle();
        toast("图像比较完成");
    }
}

// 示例5: 简单的图像处理
function imageProcessingDemo() {
    console.log("=== 图像处理演示 ===");
    
    let img = captureScreen();
    if (img) {
        // 转换为灰度图
        let grayImg = images.grayscale(img);
        if (grayImg) {
            let fileName = "/sdcard/gray_screenshot.png";
            images.save(grayImg, fileName);
            console.log("灰度图已保存: " + fileName);
            grayImg.recycle();
        }
        
        // 调整亮度
        let brightImg = images.adjustBrightness(img, 50);
        if (brightImg) {
            let fileName = "/sdcard/bright_screenshot.png";
            images.save(brightImg, fileName);
            console.log("亮度调整图已保存: " + fileName);
            brightImg.recycle();
        }
        
        img.recycle();
        toast("图像处理完成");
    }
}

// 主函数
function main() {
    let demos = [
        "基础截图演示",
        "区域截图演示",
        "颜色识别演示",
        "图像比较演示",
        "图像处理演示",
        "退出"
    ];
    
    while (true) {
        let choice = dialogs.select("选择图像处理演示", demos);
        
        switch(choice) {
            case 0:
                basicScreenshotDemo();
                break;
            case 1:
                regionScreenshotDemo();
                break;
            case 2:
                colorDetectionDemo();
                break;
            case 3:
                imageComparisonDemo();
                break;
            case 4:
                imageProcessingDemo();
                break;
            case 5:
                console.log("退出演示");
                exit();
                break;
            default:
                exit();
                break;
        }
        
        sleep(1000);
    }
}

// 运行主函数
main();
