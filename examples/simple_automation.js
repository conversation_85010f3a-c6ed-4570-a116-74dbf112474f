/**
 * 简单自动化示例
 * 演示如何自动化一些常见的手机操作
 */

// 确保无障碍服务已开启
auto.waitFor();
console.show();

// 示例1: 自动打开设置并返回
function openSettingsDemo() {
    console.log("=== 自动打开设置演示 ===");
    
    // 打开设置
    app.startActivity({
        action: "android.settings.SETTINGS"
    });
    
    toast("已打开设置，3秒后自动返回");
    sleep(3000);
    
    // 返回桌面
    back();
    sleep(1000);
    back();
    
    console.log("设置操作完成");
}

// 示例2: 自动滑动和点击
function scrollAndClickDemo() {
    console.log("=== 滑动和点击演示 ===");
    
    // 向下滑动几次
    for (let i = 0; i < 3; i++) {
        console.log("第" + (i+1) + "次向下滑动");
        swipe(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 300);
        sleep(1000);
    }
    
    // 向上滑动回到顶部
    for (let i = 0; i < 3; i++) {
        console.log("第" + (i+1) + "次向上滑动");
        swipe(device.width / 2, device.height * 0.3, device.width / 2, device.height * 0.7, 300);
        sleep(1000);
    }
    
    console.log("滑动演示完成");
}

// 示例3: 文本搜索和操作
function textSearchDemo() {
    console.log("=== 文本搜索演示 ===");
    
    // 搜索常见的文本
    let searchTexts = ["设置", "返回", "确定", "取消", "搜索"];
    
    for (let text of searchTexts) {
        let widget = textContains(text).findOne(1000);
        if (widget) {
            console.log("找到文本: " + text + " 位置: " + widget.bounds());
            // 高亮显示找到的控件
            widget.click();
            sleep(500);
        } else {
            console.log("未找到文本: " + text);
        }
    }
}

// 示例4: 模拟用户输入
function inputDemo() {
    console.log("=== 输入演示 ===");
    
    // 查找输入框
    let inputField = className("EditText").findOne(2000);
    if (inputField) {
        console.log("找到输入框");
        inputField.click();
        sleep(500);
        
        // 输入文本
        inputField.setText("这是AutoX.js自动输入的文本");
        sleep(1000);
        
        // 清空输入框
        inputField.setText("");
        console.log("输入演示完成");
    } else {
        console.log("未找到输入框");
    }
}

// 主函数
function main() {
    let demos = [
        "打开设置演示",
        "滑动和点击演示", 
        "文本搜索演示",
        "输入演示",
        "退出"
    ];
    
    while (true) {
        let choice = dialogs.select("选择要运行的演示", demos);
        
        switch(choice) {
            case 0:
                openSettingsDemo();
                break;
            case 1:
                scrollAndClickDemo();
                break;
            case 2:
                textSearchDemo();
                break;
            case 3:
                inputDemo();
                break;
            case 4:
                console.log("退出演示");
                exit();
                break;
            default:
                exit();
                break;
        }
        
        sleep(1000);
    }
}

// 运行主函数
main();
