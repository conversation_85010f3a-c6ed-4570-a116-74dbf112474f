# AutoX.js Demo 项目

这是一个简单的AutoX.js演示项目，展示了AutoX.js的基本功能和用法。

## 项目结构

```
autoJs/
├── main.js          # 主程序文件
├── project.json     # 项目配置文件
└── README.md        # 项目说明文档
```

## 功能演示

这个demo包含以下功能演示：

### 1. 基础UI操作演示
- Toast消息显示
- 对话框操作
- 用户输入获取

### 2. 文本识别演示
- 查找包含特定文本的控件
- 获取屏幕上所有文本控件
- 文本内容读取

### 3. 图像识别演示
- 屏幕截图功能
- 图片保存
- 截图权限请求

### 4. 手势操作演示
- 点击操作
- 滑动手势
- 长按操作

### 5. 应用管理演示
- 获取当前应用信息
- 查看已安装应用列表
- 启动其他应用

## 使用方法

### 前置条件
1. 安装AutoX.js应用
2. 开启无障碍服务权限
3. 授予必要的系统权限（存储、截图等）

### 运行步骤
1. 将项目文件复制到手机存储
2. 在AutoX.js中打开main.js文件
3. 点击运行按钮开始演示

### 权限说明
- **无障碍服务**: 用于UI自动化操作
- **存储权限**: 用于保存截图文件
- **悬浮窗权限**: 用于显示控制台和对话框

## 注意事项

1. **权限授予**: 首次运行时需要授予相关权限
2. **设备兼容性**: 不同Android版本可能有差异
3. **安全使用**: 仅用于学习和测试目的
4. **资源管理**: 及时释放图片等资源避免内存泄漏

## 扩展功能

可以基于这个demo继续开发：

- 添加更复杂的UI自动化流程
- 集成OCR文字识别
- 实现图像模板匹配
- 添加网络请求功能
- 实现定时任务

## 常见问题

### Q: 程序无法运行？
A: 检查是否已开启无障碍服务权限

### Q: 截图功能失败？
A: 确认已授予截图权限，部分设备需要手动开启

### Q: 找不到文本控件？
A: 确保目标应用界面已加载完成，可以增加等待时间

## 技术支持

- AutoX.js官方文档: https://docs.autojs.org/
- 社区论坛: 搜索AutoX.js相关社区

## 版本信息

- 版本: 1.0.0
- 更新日期: 2025-08-20
- 兼容性: AutoX.js 6.0+
