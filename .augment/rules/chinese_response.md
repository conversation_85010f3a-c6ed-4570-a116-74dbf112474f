---
type: "manual"
---

# 中文回复规则

## 规则描述
要求 AI 助手在回复时使用中文，提供更好的中文用户体验。

## 适用场景
- 用户使用中文提问时
- 需要中文技术文档和代码注释时
- 中文开发环境和项目

## 具体要求
1. **主要回复语言**：使用简体中文回复
2. **代码注释**：代码中的注释使用中文
3. **变量命名**：可以使用英文，但解释用中文
4. **技术术语**：优先使用中文术语，必要时可标注英文原文
5. **错误信息**：用中文解释错误和解决方案

## 示例
```javascript
// 正确示例：中文注释
function getUserInfo() {
    // 获取用户信息
    const user = getCurrentUser();
    return user;
}
```

## 例外情况
- 代码本身保持英文（函数名、变量名等）
- 官方API名称保持原文
- 特定的技术缩写（如HTTP、API、JSON等）