/**
 * 游戏辅助工具 - 独立运行版本
 * 支持多种游戏的自动化操作
 */

// 确保权限
auto.waitFor();
console.show();
console.log("游戏辅助工具启动中...");

// 全局配置
const CONFIG = {
    // 运行状态
    isRunning: false,
    currentGame: null,
    
    // 操作间隔(毫秒)
    clickInterval: 1000,
    checkInterval: 500,
    
    // 截图设置
    screenshotPath: "/sdcard/game_assistant/",
    
    // 颜色阈值
    colorThreshold: 20
};

// 创建存储目录
files.ensureDir(CONFIG.screenshotPath);

// 游戏配置数据
const GAMES = {
    "消除类游戏": {
        packageName: "com.example.match3",
        features: ["自动消除", "自动收集奖励", "自动过关"],
        colors: {
            gem_red: "#FF0000",
            gem_blue: "#0000FF", 
            gem_green: "#00FF00",
            reward_button: "#FFD700"
        }
    },
    "放置类游戏": {
        packageName: "com.example.idle",
        features: ["自动点击", "自动升级", "自动收菜"],
        colors: {
            upgrade_button: "#00FF00",
            collect_button: "#FFD700",
            click_area: "#FFFFFF"
        }
    },
    "卡牌类游戏": {
        packageName: "com.example.card",
        features: ["自动战斗", "自动抽卡", "自动任务"],
        colors: {
            battle_button: "#FF4444",
            card_button: "#4444FF",
            task_button: "#44FF44"
        }
    }
};

// 工具类
class GameAssistant {
    constructor() {
        this.isRunning = false;
        this.currentTask = null;
        this.stats = {
            clicks: 0,
            rewards: 0,
            runtime: 0
        };
    }
    
    // 初始化截图权限
    initScreenCapture() {
        if (!requestScreenCapture()) {
            dialogs.alert("错误", "需要截图权限才能运行游戏辅助");
            return false;
        }
        return true;
    }
    
    // 查找颜色位置
    findColorPosition(targetColor, region = null) {
        let img = captureScreen();
        if (!img) return null;
        
        try {
            let color = colors.parseColor(targetColor);
            let point = images.findColor(img, color, {
                threshold: CONFIG.colorThreshold,
                region: region
            });
            
            img.recycle();
            return point;
        } catch (e) {
            console.log("颜色查找错误: " + e.message);
            img.recycle();
            return null;
        }
    }
    
    // 安全点击
    safeClick(x, y) {
        if (x < 0 || y < 0 || x > device.width || y > device.height) {
            console.log("点击坐标超出屏幕范围");
            return false;
        }
        
        click(x, y);
        this.stats.clicks++;
        sleep(CONFIG.clickInterval);
        return true;
    }
    
    // 查找并点击颜色
    clickColor(targetColor, region = null) {
        let point = this.findColorPosition(targetColor, region);
        if (point) {
            console.log(`找到目标颜色 ${targetColor} 在位置 (${point.x}, ${point.y})`);
            return this.safeClick(point.x, point.y);
        }
        return false;
    }
    
    // 消除类游戏辅助
    match3GameAssist() {
        console.log("=== 消除类游戏辅助 ===");
        let game = GAMES["消除类游戏"];
        
        while (this.isRunning) {
            // 查找可消除的宝石
            for (let colorName in game.colors) {
                if (!this.isRunning) break;
                
                let color = game.colors[colorName];
                if (this.clickColor(color)) {
                    console.log(`点击了 ${colorName}`);
                }
            }
            
            // 检查奖励按钮
            if (this.clickColor(game.colors.reward_button)) {
                console.log("收集了奖励");
                this.stats.rewards++;
            }
            
            sleep(CONFIG.checkInterval);
        }
    }
    
    // 放置类游戏辅助
    idleGameAssist() {
        console.log("=== 放置类游戏辅助 ===");
        let game = GAMES["放置类游戏"];
        
        while (this.isRunning) {
            // 自动点击主界面
            if (this.clickColor(game.colors.click_area)) {
                console.log("执行了自动点击");
            }
            
            // 自动升级
            if (this.clickColor(game.colors.upgrade_button)) {
                console.log("执行了自动升级");
            }
            
            // 收集奖励
            if (this.clickColor(game.colors.collect_button)) {
                console.log("收集了奖励");
                this.stats.rewards++;
            }
            
            sleep(CONFIG.checkInterval);
        }
    }
    
    // 卡牌类游戏辅助
    cardGameAssist() {
        console.log("=== 卡牌类游戏辅助 ===");
        let game = GAMES["卡牌类游戏"];
        
        while (this.isRunning) {
            // 自动战斗
            if (this.clickColor(game.colors.battle_button)) {
                console.log("开始自动战斗");
                sleep(3000); // 等待战斗结束
            }
            
            // 自动抽卡
            if (this.clickColor(game.colors.card_button)) {
                console.log("执行了自动抽卡");
            }
            
            // 完成任务
            if (this.clickColor(game.colors.task_button)) {
                console.log("完成了任务");
                this.stats.rewards++;
            }
            
            sleep(CONFIG.checkInterval);
        }
    }
    
    // 通用游戏辅助
    universalGameAssist() {
        console.log("=== 通用游戏辅助 ===");
        
        // 常见的游戏元素颜色
        let commonColors = [
            "#FFD700", // 金色按钮
            "#00FF00", // 绿色确认
            "#FF4444", // 红色战斗
            "#4444FF", // 蓝色功能
        ];
        
        while (this.isRunning) {
            for (let color of commonColors) {
                if (!this.isRunning) break;
                
                if (this.clickColor(color)) {
                    console.log(`点击了颜色: ${color}`);
                }
            }
            
            sleep(CONFIG.checkInterval);
        }
    }
    
    // 启动辅助
    start(gameType) {
        if (this.isRunning) {
            toast("辅助已在运行中");
            return;
        }
        
        if (!this.initScreenCapture()) {
            return;
        }
        
        this.isRunning = true;
        this.stats = { clicks: 0, rewards: 0, runtime: Date.now() };
        
        console.log(`启动 ${gameType} 辅助`);
        toast(`${gameType} 辅助已启动`);
        
        // 根据游戏类型选择辅助方法
        switch (gameType) {
            case "消除类游戏":
                this.match3GameAssist();
                break;
            case "放置类游戏":
                this.idleGameAssist();
                break;
            case "卡牌类游戏":
                this.cardGameAssist();
                break;
            default:
                this.universalGameAssist();
                break;
        }
    }
    
    // 停止辅助
    stop() {
        this.isRunning = false;
        this.stats.runtime = Date.now() - this.stats.runtime;
        
        console.log("=== 运行统计 ===");
        console.log(`运行时间: ${Math.floor(this.stats.runtime / 1000)}秒`);
        console.log(`总点击次数: ${this.stats.clicks}`);
        console.log(`收集奖励: ${this.stats.rewards}`);
        
        toast("游戏辅助已停止");
    }
    
    // 显示统计信息
    showStats() {
        let runtime = this.isRunning ? Date.now() - this.stats.runtime : this.stats.runtime;
        let message = `运行时间: ${Math.floor(runtime / 1000)}秒\n` +
                     `点击次数: ${this.stats.clicks}\n` +
                     `收集奖励: ${this.stats.rewards}`;
        
        dialogs.alert("运行统计", message);
    }
}

// 创建辅助实例
let assistant = new GameAssistant();

// 主菜单
function showMainMenu() {
    let options = [
        "消除类游戏辅助",
        "放置类游戏辅助", 
        "卡牌类游戏辅助",
        "通用游戏辅助",
        "停止辅助",
        "查看统计",
        "设置",
        "退出"
    ];
    
    return dialogs.select("游戏辅助工具", options);
}

// 设置菜单
function showSettings() {
    let settings = [
        "点击间隔: " + CONFIG.clickInterval + "ms",
        "检查间隔: " + CONFIG.checkInterval + "ms", 
        "颜色阈值: " + CONFIG.colorThreshold,
        "返回主菜单"
    ];
    
    let choice = dialogs.select("设置", settings);
    
    switch (choice) {
        case 0:
            let newClickInterval = dialogs.rawInput("设置点击间隔(毫秒)", CONFIG.clickInterval.toString());
            if (newClickInterval && !isNaN(newClickInterval)) {
                CONFIG.clickInterval = parseInt(newClickInterval);
                toast("点击间隔已设置为 " + CONFIG.clickInterval + "ms");
            }
            break;
        case 1:
            let newCheckInterval = dialogs.rawInput("设置检查间隔(毫秒)", CONFIG.checkInterval.toString());
            if (newCheckInterval && !isNaN(newCheckInterval)) {
                CONFIG.checkInterval = parseInt(newCheckInterval);
                toast("检查间隔已设置为 " + CONFIG.checkInterval + "ms");
            }
            break;
        case 2:
            let newThreshold = dialogs.rawInput("设置颜色阈值(0-255)", CONFIG.colorThreshold.toString());
            if (newThreshold && !isNaN(newThreshold)) {
                CONFIG.colorThreshold = parseInt(newThreshold);
                toast("颜色阈值已设置为 " + CONFIG.colorThreshold);
            }
            break;
    }
}

// 主程序循环
function main() {
    console.log("游戏辅助工具已启动");
    toast("欢迎使用游戏辅助工具");
    
    while (true) {
        let choice = showMainMenu();
        
        switch (choice) {
            case 0:
                assistant.start("消除类游戏");
                break;
            case 1:
                assistant.start("放置类游戏");
                break;
            case 2:
                assistant.start("卡牌类游戏");
                break;
            case 3:
                assistant.start("通用游戏");
                break;
            case 4:
                assistant.stop();
                break;
            case 5:
                assistant.showStats();
                break;
            case 6:
                showSettings();
                break;
            case 7:
                assistant.stop();
                console.log("程序退出");
                exit();
                break;
            default:
                assistant.stop();
                exit();
                break;
        }
        
        sleep(500);
    }
}

// 启动程序
main();
